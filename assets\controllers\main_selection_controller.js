import { Controller } from '@hotwired/stimulus';
import { DragSelection } from '../js/utils/drag-selection.js';

/**
 * Controller Stimulus pour la sélection dans le tableau principal
 */
export default class extends Controller {
    static targets = ['container', 'indicator', 'deleteBtn', 'floatingBtn', 'selectedCount', 'floatingCount'];
    static values = {
        deleteUrl: String,
        refreshUrl: String
    };

    connect() {
        this.selectedUsers = new Set();
        this.setupDragSelection();
        this.setupScrollHandler();
    }

    disconnect() {
        if (this.dragSelection) {
            this.dragSelection.destroy();
        }
        if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
        }
    }

    /**
     * Configure le système de drag-to-select
     */
    setupDragSelection() {
        this.dragSelection = new DragSelection(this.containerTarget, {
            useContainer: false, // Coordonnées absolues pour le tableau principal
            autoScroll: true,
            onSelectionChange: (selectedItems) => {
                this.selectedUsers = selectedItems;
                this.updateUI();
            }
        });
    }

    /**
     * Configure le gestionnaire de scroll pour le bouton flottant
     */
    setupScrollHandler() {
        let ticking = false;
        this.scrollHandler = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateFloatingButtonVisibility();
                    ticking = false;
                });
                ticking = true;
            }
        };
        window.addEventListener('scroll', this.scrollHandler, { passive: true });
    }

    /**
     * Met à jour l'interface utilisateur
     */
    updateUI() {
        const count = this.selectedUsers.size;

        // Mettre à jour les compteurs
        this.selectedCountTarget.textContent = `${count} utilisateur(s) sélectionné(s)`;
        this.floatingCountTarget.textContent = `Supprimer ${count}`;

        if (count > 0) {
            this.indicatorTarget.classList.remove('hidden');
            this.deleteBtnTarget.classList.remove('hidden');
            this.deleteBtnTarget.textContent = `Supprimer ${count} utilisateur(s)`;

            // Gérer l'affichage du bouton flottant
            setTimeout(() => this.updateFloatingButtonVisibility(), 0);
        } else {
            this.indicatorTarget.classList.add('hidden');
            this.deleteBtnTarget.classList.add('hidden');
            this.floatingBtnTarget.classList.add('hidden');
        }
    }

    /**
     * Gère la visibilité du bouton flottant
     */
    updateFloatingButtonVisibility() {
        const count = this.selectedUsers.size;
        if (count === 0) return;

        const indicatorRect = this.indicatorTarget.getBoundingClientRect();
        const isIndicatorVisible = indicatorRect.bottom > 0 && indicatorRect.top < window.innerHeight;

        // Afficher le bouton flottant seulement si l'indicateur n'est pas visible
        if (isIndicatorVisible) {
            this.floatingBtnTarget.classList.add('hidden');
        } else {
            this.floatingBtnTarget.classList.remove('hidden');
        }
    }

    /**
     * Sélectionne tous les utilisateurs
     */
    selectAll() {
        this.dragSelection.selectAll();
    }

    /**
     * Désélectionne tous les utilisateurs
     */
    clearSelection() {
        this.dragSelection.clearSelection();
    }

    /**
     * Supprime les utilisateurs sélectionnés
     */
    async deleteSelected() {
        if (this.selectedUsers.size === 0) {
            window.showToast?.warning('Veuillez sélectionner au moins un utilisateur à supprimer');
            return;
        }

        const confirmed = await window.showConfirm?.({
            title: 'Suppression multiple',
            message: `Êtes-vous sûr de vouloir retirer ${this.selectedUsers.size} utilisateur(s) d'OSI ? Cette action est irréversible.`,
            confirmText: 'Supprimer',
            cancelText: 'Annuler',
            type: 'danger',
            icon: '🗑️'
        });

        if (!confirmed) return;

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Suppression des utilisateurs en cours...');

        try {
            const response = await window.ajax.post(this.deleteUrlValue, {
                userIds: Array.from(this.selectedUsers)
            });

            const result = response.data || response;

            if (result.success) {
                window.showToast?.success(result.message);

                // Supprimer les lignes du tableau sans recharger la page
                this.selectedUsers.forEach(userId => {
                    const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                    if (row) {
                        row.remove();
                    }
                });

                // Réinitialiser la sélection
                this.clearSelection();
            } else {
                window.showToast?.error('Erreur : ' + (result.message || 'Erreur inconnue'));
            }
        } catch (error) {
            console.error('Erreur lors de la suppression des utilisateurs:', error);
            window.showToast?.error('Erreur lors de la suppression des utilisateurs: ' + (error.message || 'Erreur inconnue'));
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Rafraîchit le tableau des utilisateurs
     */
    async refreshTable() {
        try {
            const response = await window.ajax.get(this.refreshUrlValue);
            const result = response.data || response;
            const users = result.data || result;

            // containerTarget est déjà le <tbody>
            if (!this.containerTarget) return;

            // Reconstruire le contenu du tableau
            this.containerTarget.innerHTML = users.map(user => this.renderUserRow(user)).join('');

            // Réinitialiser la sélection
            this.clearSelection();
            this.setupDragSelection();

        } catch (error) {
            console.error('Erreur lors du rafraîchissement du tableau:', error);
            // En cas d'erreur, on fait un reload complet
            location.reload();
        }
    }

    /**
     * Génère le HTML d'une ligne utilisateur
     */
    renderUserRow(user) {
        return `
            <tr class="user-item cursor-pointer transition-all duration-200 hover:bg-gray-50" data-user-id="${user.id}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                <span class="text-white font-medium">${(user.prenom?.[0] || '')}${(user.nom?.[0] || '')}</span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${user.nomComplet || ''}</div>
                            <div class="text-sm text-gray-500">${user.telephone || 'N/A'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email || ''}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.roleDisplay || 'Utilisateur'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${user.horaireHebdo ? user.horaireHebdo + 'h/semaine' : 'Non défini'}
                    ${user.forfaitJour ? '<span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Forfait jour</span>' : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${user.actif ?
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Actif</span>' :
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactif</span>'
                    }
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <a href="/users/${user.id}/detail" class="text-blue-600 hover:text-blue-900">Voir</a>
                        <button data-action="click->user-management#editUser" data-user-id="${user.id}" class="text-indigo-600 hover:text-indigo-900">Modifier</button>
                        <button data-action="click->user-management#deleteUser" data-user-id="${user.id}" data-user-name="${(user.nomComplet || '').replace(/'/g, "\\'")}" class="text-red-600 hover:text-red-900">Supprimer</button>
                    </div>
                </td>
            </tr>
        `;
    }
}
