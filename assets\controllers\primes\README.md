# Contrôleurs Primes

Ce dossier contient les contrôleurs Stimulus pour la gestion des primes journalières.

## Contrôleurs disponibles

### `primes_management_controller.js`
- **Usage** : Gestion complète des primes journalières
- **Identifiant** : `primes-management`
- **Page** : `/primes`
- **Description** : Contrôleur principal pour la page de gestion des primes

#### Fonctionnalités
- ✅ Filtrage des missions (utilisateur, zone, niveau, dates)
- ✅ Calcul automatique des primes par mission et utilisateur
- ✅ Affichage des statistiques (total primes, heures réelles/théoriques)
- ✅ Calcul automatique du mois en cours au chargement
- ✅ Spinner de chargement
- ✅ Gestion des erreurs
- ✅ Intégration avec l'API des missions et primes

#### Targets principaux
- `userPrimes`, `zonePrimes`, `niveauPrimes`, `dateDebutPrimes`, `dateFinPrimes` : Filtres
- `primesTableBody`, `primesTable`, `noPrimesMessage` : Tableau des données
- `loadingSpinner`, `calculerButton`, `calculerText` : État de chargement

#### Actions principales
- `calculerPrimes()` : Calcule les primes selon les filtres
- `calculerPrimesMoisEnCours()` : Calcule les primes du mois courant
- `definirDatesMoisEnCours()` : Définit les dates du mois en cours
- `loadUsers()` : Charge la liste des utilisateurs

#### Values (URLs API)
- `usersApiUrl` : URL pour récupérer les utilisateurs
- `missionsApiUrl` : URL pour récupérer les missions avec filtres
- `missionPrimesUrl` : URL pour calculer les primes d'une mission
- `primeDetailUrl` : URL pour voir le détail des primes

## Architecture

Le contrôleur suit un processus en 2 étapes :

### 1. Récupération des missions
- Appel à l'API `/api/missions` avec les filtres
- Récupération de la liste des missions correspondantes

### 2. Calcul des primes
- Pour chaque mission, appel à `/api/missions/{id}/primes`
- Récupération des primes par utilisateur
- Agrégation des données pour l'affichage

## Logique métier

### Calcul des primes
- **Zone Euro** : Barème différent de la zone Hors Euro
- **Niveau 1/2** : Montants différents selon le niveau
- **Type de jour** : Semaine, weekend travaillé/non travaillé
- **Répartition** : Prime totale divisée par nombre d'utilisateurs

### Statistiques affichées
- **Total primes** : Somme de toutes les primes calculées
- **Nombre d'assignations** : Nombre d'utilisateurs assignés aux missions
- **Heures réelles/théoriques** : Comparaison effort réel vs théorique

## Intégration avec l'API

Le contrôleur utilise plusieurs endpoints :
- `GET /api/users` : Liste des utilisateurs
- `GET /api/missions?filters` : Missions filtrées
- `GET /api/missions/{id}/primes` : Primes d'une mission
- `GET /primes/{id}` : Page détail des primes

## Gestion d'erreurs

- **Missions non trouvées** : Message "Aucune mission trouvée"
- **Erreurs API** : Toast d'erreur + log console
- **Missions sans utilisateurs** : Affichage "Non assigné"
- **Données manquantes** : Valeurs par défaut (0, 'N/A')
