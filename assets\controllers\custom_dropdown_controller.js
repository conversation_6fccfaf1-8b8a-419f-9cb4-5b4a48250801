import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour les dropdowns personnalisés
 */
export default class extends Controller {
    static targets = ['button', 'dropdown', 'hiddenInput', 'buttonText', 'buttonIcon'];
    static values = {
        selected: String,
        name: String,
        required: <PERSON><PERSON><PERSON>
    };

    connect() {
        this.isOpen = false;
        this.setupEventListeners();

        // Initialiser avec la valeur sélectionnée si elle existe
        if (this.selectedValue) {
            this.selectOption(this.selectedValue, true);
        }

        // Écouter les événements de suggestion de zone
        this.element.addEventListener('zone:suggest', this.handleZoneSuggest.bind(this));
    }

    disconnect() {
        document.removeEventListener('click', this.handleDocumentClick.bind(this));
        this.element.removeEventListener('zone:suggest', this.handleZoneSuggest.bind(this));
    }

    setupEventListeners() {
        // Listener pour fermer le dropdown en cliquant ailleurs
        document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    /**
     * Toggle l'ouverture/fermeture du dropdown
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    /**
     * Ouvre le dropdown
     */
    open() {
        this.dropdownTarget.classList.remove('hidden');
        this.isOpen = true;

        // Ajouter une classe active au bouton
        this.buttonTarget.classList.add('ring-4', 'ring-gray-100');
    }

    /**
     * Ferme le dropdown
     */
    close() {
        this.dropdownTarget.classList.add('hidden');
        this.isOpen = false;

        // Retirer la classe active du bouton
        this.buttonTarget.classList.remove('ring-4', 'ring-gray-100');
    }

    /**
     * Sélectionne une option
     */
    selectOption(event, skipClose = false) {
        let value, text, icon;

        if (typeof event === 'string') {
            // Appelé programmatiquement avec une valeur
            value = event;
            const option = this.dropdownTarget.querySelector(`[data-value="${value}"]`);
            if (option) {
                text = option.dataset.text || option.textContent.trim();
                icon = option.dataset.icon || '';
            }
        } else {
            // Appelé par un clic sur une option
            const option = event.currentTarget;
            value = option.dataset.value || ''; // Permettre les valeurs vides
            text = option.dataset.text || option.textContent.trim();
            icon = option.dataset.icon || '';
        }

        // Permettre la sélection même si la valeur est vide (pour "Tous")
        if (value === undefined) return;

        // Mettre à jour le bouton
        if (this.hasButtonTextTarget) {
            this.buttonTextTarget.textContent = text;
        }

        if (this.hasButtonIconTarget && icon) {
            this.buttonIconTarget.innerHTML = icon;
        }

        // Mettre à jour l'input caché
        if (this.hasHiddenInputTarget) {
            this.hiddenInputTarget.value = value;
            // Déclencher l'événement change pour la validation des formulaires
            this.hiddenInputTarget.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Mettre à jour les classes des options
        this.updateOptionStates(value);

        // Stocker la valeur sélectionnée
        this.selectedValue = value;

        if (!skipClose) {
            this.close();
        }
    }

    /**
     * Met à jour l'état visuel des options
     */
    updateOptionStates(selectedValue) {
        const options = this.dropdownTarget.querySelectorAll('[data-value]');
        options.forEach(option => {
            const optionValue = option.dataset.value || '';
            if (optionValue === selectedValue) {
                option.classList.add('bg-blue-50', 'text-blue-700');
                option.classList.remove('text-gray-700');
            } else {
                option.classList.remove('bg-blue-50', 'text-blue-700');
                option.classList.add('text-gray-700');
            }
        });
    }

    /**
     * Gère les clics en dehors du dropdown
     */
    handleDocumentClick(event) {
        if (!this.element.contains(event.target)) {
            this.close();
        }
    }

    /**
     * Gère les touches du clavier
     */
    handleKeydown(event) {
        if (!this.isOpen) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.open();
            }
            return;
        }

        switch (event.key) {
            case 'Escape':
                this.close();
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.navigateOptions(1);
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.navigateOptions(-1);
                break;
            case 'Enter':
                event.preventDefault();
                const focused = this.dropdownTarget.querySelector('.keyboard-focus');
                if (focused) {
                    this.selectOption({ currentTarget: focused });
                }
                break;
        }
    }

    /**
     * Navigation au clavier dans les options
     */
    navigateOptions(direction) {
        const options = Array.from(this.dropdownTarget.querySelectorAll('[data-value]'));
        const currentIndex = options.findIndex(option =>
            option.classList.contains('keyboard-focus')
        );

        let newIndex = currentIndex + direction;
        if (newIndex < 0) newIndex = options.length - 1;
        if (newIndex >= options.length) newIndex = 0;

        // Retirer le focus de l'option actuelle
        if (currentIndex >= 0) {
            options[currentIndex].classList.remove('keyboard-focus', 'bg-gray-100');
        }

        // Ajouter le focus à la nouvelle option
        options[newIndex].classList.add('keyboard-focus', 'bg-gray-100');
        options[newIndex].scrollIntoView({ block: 'nearest' });
    }

    /**
     * Active le dropdown (pour les dropdowns conditionnels comme Zone)
     */
    enable() {
        this.buttonTarget.disabled = false;
        this.buttonTarget.classList.remove('bg-gray-50');
        this.buttonTarget.classList.add('bg-white');
    }

    /**
     * Désactive le dropdown (pour les dropdowns conditionnels comme Zone)
     */
    disable() {
        this.buttonTarget.disabled = true;
        this.buttonTarget.classList.add('bg-gray-50');
        this.buttonTarget.classList.remove('bg-white');
        this.close();
    }

    /**
     * Met à jour les options disponibles (pour les dropdowns dynamiques)
     */
    updateOptions(options) {
        const dropdown = this.dropdownTarget;
        dropdown.innerHTML = '';

        options.forEach(option => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'custom-dropdown-option';
            button.dataset.value = option.value;
            button.dataset.text = option.text;
            button.dataset.action = 'click->custom-dropdown#selectOption';
            button.innerHTML = option.html || option.text;
            dropdown.appendChild(button);
        });
    }

    /**
     * Gère les suggestions de zone depuis country-autocomplete
     */
    handleZoneSuggest(event) {
        const { zone, enabled } = event.detail;

        // Le dropdown reste toujours activé, on suggère la zone appropriée
        if (enabled && zone) {
            // Suggérer la zone appropriée - toujours sélectionner la zone du pays
            this.selectOption(zone, true);
        }
        // Le dropdown reste toujours utilisable pour modification manuelle
    }
}
