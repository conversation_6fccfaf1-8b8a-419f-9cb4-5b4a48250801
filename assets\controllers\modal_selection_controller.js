import { Controller } from '@hotwired/stimulus';
import { DragSelection } from '../js/utils/drag-selection.js';
import { debounce } from '../js/utils/throttle.js';

/**
 * Controller Stimulus pour la sélection dans le modal d'ajout d'utilisateurs
 */
export default class extends Controller {
    static targets = ['modal', 'container', 'searchInput', 'selectedCount'];
    static values = {
        loadUsersUrl: String,
        addUsersUrl: String
    };

    connect() {
        this.selectedUsers = new Set();
        this.availableUsers = [];
        this.setupSearchHandler();
    }

    disconnect() {
        if (this.dragSelection) {
            this.dragSelection.destroy();
        }
    }

    /**
     * Configure le gestionnaire de recherche avec debounce
     */
    setupSearchHandler() {
        this.debouncedSearch = debounce(() => {
            this.renderUsersList();
        }, 300);

        if (this.hasSearchInputTarget) {
            this.searchInputTarget.addEventListener('input', this.debouncedSearch);
        }
    }

    /**
     * Ouvre le modal et charge les utilisateurs
     */
    async open() {
        this.modalTarget.classList.remove('hidden');
        await this.loadAvailableUsers();
    }

    /**
     * Ferme le modal
     */
    close() {
        // Nettoyer le drag en cours si nécessaire
        if (this.dragSelection) {
            this.dragSelection.cleanup();
        }

        this.modalTarget.classList.add('hidden');
        this.selectedUsers.clear();
        this.updateSelectedCount();
    }

    /**
     * Ferme le modal en cliquant à l'extérieur
     */
    closeOnBackdrop(event) {
        if (event.target === this.modalTarget) {
            this.close();
        }
    }

    /**
     * Charge les utilisateurs disponibles depuis l'API
     */
    async loadAvailableUsers() {
        try {
            const response = await window.ajax.get(this.loadUsersUrlValue);
            this.availableUsers = response.data || response;
            this.renderUsersList();
        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
            this.containerTarget.innerHTML = '<p class="text-red-500 p-4">Erreur lors du chargement des utilisateurs</p>';
            window.showToast?.error('Erreur lors du chargement des utilisateurs disponibles');
        }
    }

    /**
     * Rend la liste des utilisateurs avec filtrage
     */
    renderUsersList() {
        const searchTerm = this.hasSearchInputTarget ? this.searchInputTarget.value.toLowerCase() : '';

        const filteredUsers = this.availableUsers.filter(user =>
            user.nom?.toLowerCase().includes(searchTerm) ||
            user.prenom?.toLowerCase().includes(searchTerm) ||
            user.email?.toLowerCase().includes(searchTerm) ||
            user.username?.toLowerCase().includes(searchTerm)
        );

        if (filteredUsers.length === 0) {
            this.containerTarget.innerHTML = '<p class="text-gray-500 p-4">Aucun utilisateur disponible</p>';
            return;
        }

        this.containerTarget.innerHTML = filteredUsers.map(user => this.renderUserItem(user)).join('');
        this.setupUserSelection();
    }

    /**
     * Génère le HTML d'un élément utilisateur
     */
    renderUserItem(user) {
        return `
            <div class="user-item p-3 border-b border-gray-200 flex items-center justify-between" data-user-id="${user.id}">
                <div class="flex items-center space-x-3">
                    <input type="checkbox" class="user-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-user-id="${user.id}">
                    <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-white text-xs font-medium">${(user.prenom?.[0] || '')}${(user.nom?.[0] || '')}</span>
                        </div>
                    </div>
                    <div>
                        <div class="text-sm font-medium text-gray-900">${user.prenom || ''} ${user.nom || ''}</div>
                        <div class="text-sm text-gray-500">${user.email || ''} ${user.username ? '(' + user.username + ')' : ''}</div>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    ${user.secteur || 'N/A'}
                </div>
            </div>
        `;
    }

    /**
     * Configure la sélection des utilisateurs
     */
    setupUserSelection() {
        // Gestion des checkboxes avec délégation d'événements
        this.containerTarget.addEventListener('change', (event) => {
            if (event.target.classList.contains('user-checkbox')) {
                this.handleCheckboxChange(event.target);
            }
        });

        // Configuration du drag-to-select
        this.setupDragSelection();
    }

    /**
     * Configure le système de drag-to-select pour le modal
     */
    setupDragSelection() {
        if (this.dragSelection) {
            this.dragSelection.destroy();
        }

        this.dragSelection = new DragSelection(this.containerTarget, {
            useContainer: true, // Coordonnées relatives au conteneur pour le modal
            autoScroll: true,
            onSelectionChange: (selectedItems) => {
                this.selectedUsers = selectedItems;
                this.updateSelectedCount();
                this.syncCheckboxes();
            },
            onItemClick: (item, itemId) => {
                // Synchroniser la checkbox lors d'un clic
                const checkbox = item.querySelector('.user-checkbox');
                if (checkbox) {
                    checkbox.checked = this.selectedUsers.has(itemId);
                }
            }
        });
    }

    /**
     * Gère le changement d'état d'une checkbox
     */
    handleCheckboxChange(checkbox) {
        const userId = parseInt(checkbox.dataset.userId);
        const userItem = checkbox.closest('.user-item');

        if (checkbox.checked) {
            this.selectedUsers.add(userId);
            userItem.classList.add('selected');
        } else {
            this.selectedUsers.delete(userId);
            userItem.classList.remove('selected');
        }

        this.updateSelectedCount();
    }

    /**
     * Synchronise les checkboxes avec la sélection
     */
    syncCheckboxes() {
        const checkboxes = this.containerTarget.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            const userId = parseInt(checkbox.dataset.userId);
            checkbox.checked = this.selectedUsers.has(userId);
        });
    }

    /**
     * Met à jour le compteur de sélection
     */
    updateSelectedCount() {
        if (this.hasSelectedCountTarget) {
            this.selectedCountTarget.textContent = `${this.selectedUsers.size} utilisateur(s) sélectionné(s)`;
        }
    }

    /**
     * Sélectionne tous les utilisateurs
     */
    selectAll() {
        const checkboxes = this.containerTarget.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                this.handleCheckboxChange(checkbox);
            }
        });
    }

    /**
     * Désélectionne tous les utilisateurs
     */
    clearSelection() {
        this.selectedUsers.clear();
        const checkboxes = this.containerTarget.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.closest('.user-item').classList.remove('selected');
        });
        this.updateSelectedCount();
    }

    /**
     * Ajoute les utilisateurs sélectionnés
     */
    async addSelectedUsers(event) {
        if (this.selectedUsers.size === 0) {
            window.showToast?.warning('Veuillez sélectionner au moins un utilisateur');
            return;
        }

        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = 'Ajout en cours...';
        btn.disabled = true;

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Ajout des utilisateurs en cours...');

        try {
            const response = await window.ajax.post(this.addUsersUrlValue, {
                userIds: Array.from(this.selectedUsers)
            });

            const result = response.data || response;

            if (result.success) {
                window.showToast?.success(result.message);
                this.close();

                // Déclencher un événement pour rafraîchir le tableau principal
                this.dispatch('usersAdded', { bubbles: true });
            } else {
                window.showToast?.error('Erreur : ' + (result.message || 'Erreur inconnue'));
            }
        } catch (error) {
            console.error('Erreur lors de l\'ajout des utilisateurs:', error);
            window.showToast?.error('Erreur lors de l\'ajout des utilisateurs: ' + (error.message || 'Erreur inconnue'));
        } finally {
            btn.textContent = originalText;
            btn.disabled = false;
            window.loadingSpinner?.hide();
        }
    }
}
