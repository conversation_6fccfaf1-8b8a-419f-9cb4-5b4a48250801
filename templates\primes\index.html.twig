{% extends 'base.html.twig' %}

{% block title %}Gestion des primes - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Gestion des primes</h1>
        <p class="mt-2 text-gray-600">Calcul et suivi des primes journalières</p>
        <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p class="text-sm text-blue-800">
                <span class="font-medium">📅 Affichage automatique :</span>
                Les primes du mois en cours sont calculées automatiquement au chargement de la page.
            </p>
        </div>
    </div>

    <!-- Barème des primes -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Barème des primes journalières</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-blue-900 mb-3">Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-blue-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Hors Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-green-900 mb-3">Hors Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-green-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-6">
                <div>
                    <label for="userPrimes" class="block text-sm font-medium text-gray-700">Utilisateur</label>
                    <select id="userPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les utilisateurs</option>
                    </select>
                </div>
                <div>
                    <label for="zonePrimes" class="block text-sm font-medium text-gray-700">Zone</label>
                    <select id="zonePrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Toutes les zones</option>
                        <option value="EURO">Zone Euro</option>
                        <option value="HORS_EURO">Hors Zone Euro</option>
                    </select>
                </div>
                <div>
                    <label for="niveauPrimes" class="block text-sm font-medium text-gray-700">Niveau</label>
                    <select id="niveauPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les niveaux</option>
                        <option value="1">Niveau 1</option>
                        <option value="2">Niveau 2</option>
                    </select>
                </div>
                <div>
                    <label for="dateDebutPrimes" class="block text-sm font-medium text-gray-700">Date début</label>
                    <input type="date" id="dateDebutPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="dateFinPrimes" class="block text-sm font-medium text-gray-700">Date fin</label>
                    <input type="date" id="dateFinPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div class="flex items-end space-x-3">
                    <button type="button" onclick="calculerPrimes()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                        Calculer
                    </button>
                    <button type="button" onclick="calculerPrimesMoisEnCours()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                        📅 Mois en cours
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats des primes -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Calcul des primes</h3>

            <!-- Résumé -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6" id="resumePrimes" style="display: none;">
                <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total primes</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="totalPrimes">0€</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">📊</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Assignations</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="nombreMissions">0</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">⏱️</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Heures réelles / théoriques</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="primeMoyenne">0h / 0h</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau détaillé -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="primesTable" style="display: none;">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Niveau</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures réelles</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prime individuelle</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="primesTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>

            <div id="noPrimesMessage" class="text-center py-8 text-gray-500">
                Sélectionnez des critères et cliquez sur "Calculer" pour voir les primes
            </div>
        </div>
    </div>
</div>

<script>
// Encapsuler le code dans DOMContentLoaded pour éviter les conflits Turbo
document.addEventListener('DOMContentLoaded', function() {

// URLs pour les appels API
const apiUrls = {
    missionPrimes: "{{ path('api_mission_primes', {id: 'MISSION_ID'}) }}",
    primeDetail: "{{ path('app_prime_detail', {id: 'MISSION_ID'}) }}"
};

// Fonction pour définir les dates du mois en cours
function definirDateseMoisEnCours() {
    const maintenant = new Date();
    const annee = maintenant.getFullYear();
    const mois = maintenant.getMonth(); // 0-11

    // Premier jour du mois
    const premierJour = new Date(annee, mois, 1);
    const premierJourStr = premierJour.toISOString().split('T')[0];

    // Dernier jour du mois
    const dernierJour = new Date(annee, mois + 1, 0);
    const dernierJourStr = dernierJour.toISOString().split('T')[0];

    // Définir les valeurs dans les champs
    document.getElementById('dateDebutPrimes').value = premierJourStr;
    document.getElementById('dateFinPrimes').value = dernierJourStr;

    console.log(`Dates du mois en cours définies: ${premierJourStr} au ${dernierJourStr}`);
}

// Charger les utilisateurs au démarrage et calculer automatiquement les primes du mois en cours
// (Intégré dans le DOMContentLoaded principal)
async function initializePage() {
    try {
        // Définir les dates du mois en cours
        definirDateseMoisEnCours();

        // Charger les utilisateurs
        const response = await window.ajax.get("{{ path('api_user_index') }}");
        const select = document.getElementById('userPrimes');

        console.log('Réponse API users:', response);

        // L'API peut retourner directement un tableau ou un objet avec data
        const users = Array.isArray(response) ? response : (response.data || []);

        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.prenom} ${user.nom}`;
            select.appendChild(option);
        });

        console.log(`${users.length} utilisateurs chargés`);

        // Calculer automatiquement les primes du mois en cours
        console.log('Calcul automatique des primes du mois en cours...');
        await calculerPrimes();

    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
    }
}

// Fonction pour calculer rapidement les primes du mois en cours
async function calculerPrimesMoisEnCours() {
    // Réinitialiser tous les filtres
    document.getElementById('userPrimes').value = '';
    document.getElementById('zonePrimes').value = '';
    document.getElementById('niveauPrimes').value = '';

    // Définir les dates du mois en cours
    definirDateseMoisEnCours();

    // Calculer les primes
    await calculerPrimes();
}

async function calculerPrimes() {
    const userId = document.getElementById('userPrimes').value;
    const zone = document.getElementById('zonePrimes').value;
    const niveau = document.getElementById('niveauPrimes').value;
    const dateDebut = document.getElementById('dateDebutPrimes').value;
    const dateFin = document.getElementById('dateFinPrimes').value;

    try {
        // Construire l'URL avec les paramètres
        let url = "{{ path('api_mission_index') }}";
        const params = new URLSearchParams();

        if (userId) params.append('user', userId);
        if (zone) params.append('zone', zone);
        if (niveau) params.append('niveau', niveau);
        if (dateDebut) params.append('debut', dateDebut);
        if (dateFin) params.append('fin', dateFin);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await window.ajax.get(url);
        const missions = Array.isArray(response) ? response : response.data || [];

        // Calculer les primes pour chaque mission et chaque utilisateur
        const primesData = [];
        let totalPrimes = 0;
        let totalUtilisateurs = 0;

        for (const mission of missions) {
            try {
                const primeUrl = apiUrls.missionPrimes.replace('MISSION_ID', mission.id);
                const primeResponse = await window.ajax.get(primeUrl);

                // Si la mission a des utilisateurs assignés, créer une ligne par utilisateur
                if (primeResponse.primesParUtilisateur && primeResponse.primesParUtilisateur.length > 0) {
                    primeResponse.primesParUtilisateur.forEach(primeUser => {
                        const primeData = {
                            mission: mission,
                            user: primeUser.user,
                            prime: primeUser.prime,
                            heuresReelles: primeUser.heuresReelles || 0,
                            dureeJours: primeResponse.dureeJours,
                            dateDebut: primeResponse.dateDebut,
                            dateFin: primeResponse.dateFin
                        };
                        primesData.push(primeData);
                        totalPrimes += primeData.prime;
                        totalUtilisateurs++;
                    });
                } else {
                    // Mission sans utilisateurs assignés
                    const primeData = {
                        mission: mission,
                        user: null,
                        prime: 0,
                        heuresReelles: 0,
                        dureeJours: primeResponse.dureeJours,
                        dateDebut: primeResponse.dateDebut,
                        dateFin: primeResponse.dateFin
                    };
                    primesData.push(primeData);
                }
            } catch (error) {
                console.error(`Erreur lors du calcul de la prime pour la mission ${mission.id}:`, error);
            }
        }

        displayPrimesResults(primesData, totalPrimes, totalUtilisateurs);

    } catch (error) {
        console.error('Erreur lors du calcul des primes:', error);
        alert('Erreur lors du calcul des primes');
    }
}

function displayPrimesResults(primesData, totalPrimes, totalUtilisateurs) {
    // Calculer les statistiques d'heures
    const totalHeuresReelles = primesData.reduce((sum, data) => sum + (data.heuresReelles || 0), 0);
    const totalJoursTheo = primesData.reduce((sum, data) => sum + (data.dureeJours || 0), 0);
    const totalHeuresTheo = totalJoursTheo * 8;
    const ratioGlobal = totalHeuresTheo > 0 ? (totalHeuresReelles / totalHeuresTheo * 100) : 0;

    // Afficher le résumé
    document.getElementById('resumePrimes').style.display = 'grid';
    document.getElementById('totalPrimes').textContent = totalPrimes.toFixed(2) + '€';
    document.getElementById('nombreMissions').textContent = `${totalUtilisateurs || primesData.length} assignations`;
    document.getElementById('primeMoyenne').textContent = `${totalHeuresReelles.toFixed(0)}h réelles / ${totalHeuresTheo}h théo. (${ratioGlobal.toFixed(0)}%)`;

    // Afficher le tableau
    const tbody = document.getElementById('primesTableBody');
    tbody.innerHTML = '';

    if (primesData.length > 0) {
        document.getElementById('primesTable').style.display = 'table';
        document.getElementById('noPrimesMessage').style.display = 'none';

        primesData.forEach(data => {
            const mission = data.mission;
            const user = data.user;

            // Nom de l'utilisateur ou "Non assigné"
            const nomUtilisateur = user ? `${user.prenom} ${user.nom}` : 'Non assigné';

            // Période de la mission
            const periode = data.dateDebut && data.dateFin
                ? `${data.dateDebut} au ${data.dateFin}`
                : 'Non définie';

            // Calculer le ratio heures réelles / durée théorique
            const dureeTheorique = data.dureeJours * 8; // 8h par jour théorique
            const ratioEffort = dureeTheorique > 0 ? (data.heuresReelles / dureeTheorique * 100) : 0;
            const colorRatio = ratioEffort > 100 ? 'text-red-600' : ratioEffort > 80 ? 'text-orange-600' : 'text-green-600';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mission.titre}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${nomUtilisateur}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mission.pays}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${mission.zone === 'EURO' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                        ${mission.zone === 'EURO' ? 'Zone Euro' : 'Hors Zone Euro'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Niveau ${mission.niveau}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${periode}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${data.dureeJours || 0} jour(s)
                    <div class="text-xs text-gray-500">${dureeTheorique}h théo.</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="font-medium">${data.heuresReelles.toFixed(1)}h</div>
                    <div class="text-xs ${colorRatio}">${ratioEffort.toFixed(0)}% effort</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${data.prime.toFixed(2)}€</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="${apiUrls.primeDetail.replace('MISSION_ID', mission.id)}" class="text-blue-600 hover:text-blue-900">Détail primes</a>
                </td>
            `;
            tbody.appendChild(row);
        });
    } else {
        document.getElementById('primesTable').style.display = 'none';
        document.getElementById('noPrimesMessage').style.display = 'block';
        document.getElementById('noPrimesMessage').textContent = 'Aucune mission trouvée pour les critères sélectionnés';
    }
}

// Initialiser la page au chargement
initializePage();

}); // Fin de DOMContentLoaded
</script>
{% endblock %}
