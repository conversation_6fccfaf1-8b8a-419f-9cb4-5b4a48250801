import { startStimulusApp } from '@symfony/stimulus-bundle';

// Import des controllers personnalisés
import MainSelectionController from './controllers/main_selection_controller.js';
import ModalSelectionController from './controllers/modal_selection_controller.js';
import UserManagementController from './controllers/user_management_controller.js';
import MissionManagementController from './controllers/mission_management_controller.js';
import MissionModalController from './controllers/mission_modal_controller.js';
import MissionUserSelectionController from './controllers/mission_user_selection_controller.js';
import CalendarController from './controllers/calendar_controller.js';
import HeuresManagementController from './controllers/heures_management_controller.js';

const app = startStimulusApp();

// Réduire les logs Stimulus si pas en mode debug
if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
    app.debug = false;
}

// Enregistrement des controllers personnalisés
app.register('main-selection', MainSelectionController);
app.register('modal-selection', ModalSelectionController);
app.register('user-management', UserManagementController);
app.register('mission-management', MissionManagementController);
app.register('mission-modal', MissionModalController);
app.register('mission-user-selection', MissionUserSelectionController);
app.register('calendar', CalendarController);
app.register('heures-management', HeuresManagementController);
